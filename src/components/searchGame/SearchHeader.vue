<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 16:50:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-30 13:41:44
 * @FilePath     : /src/components/searchGame/SearchHeader.vue
 * @Description  : 搜索页面导航条组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 16:50:00
-->

<template>
    <!-- 自定义导航栏 -->
    <div class="pop-title">
        <button class="nav-back-button" type="button" @click="handleBack">
            <svg class="back-icon" aria-hidden="true">
                <use href="#icon-svg-JiantouL" fill=""></use>
            </svg>
        </button>
        <span class="title">Search</span>
        <div class="nav-actions">
            <!-- 右侧可以添加其他操作按钮 -->
        </div>
    </div>
</template>

<script setup lang="ts">
// 定义事件
const emit = defineEmits<{
    close: []
}>()

const handleBack = () => {
    // 触发关闭事件
    emit('close')
}
</script>

<style lang="scss" scoped>
$bg-primary: #2b2b2b;
$bg-secondary: #383838;
$bg-hover: #4a4a4a;
$bg-button: #464f50;
$text-primary: #ffffff;
$accent-color: #4caf50;

.pop-title {
    position: sticky;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 27.9122px;
    font-weight: 800;
    line-height: 55.8244px;
    height: 97.6927px;
    min-height: 97.6927px;
    background-color: rgb(50, 55, 56, 1);
    z-index: 100;

    .nav-back-button {
        width: 55.8244px;
        height: 55.8244px;
        border-radius: 10.4671px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: transparent;
        position: absolute;
        left: 27.9122px;
        top: 20.9341px;
        right: auto;
        background-color: $bg-button;
        border: none;
        cursor: pointer;

        .back-icon {
            width: 31.4012px;
            height: 31.4012px;
            fill: rgb(179, 190, 193, 1);
        }
    }

    .title {
        font-size: 27.9122px;
        color: $text-primary;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        white-space: nowrap;
    }

    .nav-actions {
        position: absolute;
        right: 6.978px;
        margin-left: auto;
        height: 100%;
        display: flex;
        align-items: center;
    }
}
</style>
