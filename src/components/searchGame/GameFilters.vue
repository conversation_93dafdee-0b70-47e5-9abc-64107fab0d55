<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 16:53:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-30 13:55:11
 * @FilePath     : /src/components/searchGame/GameFilters.vue
 * @Description  : 游戏筛选器组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 16:53:00
-->

<template>
    <div class="filters">
        <div class="filter-button" @click="handleSortClick">
            <span class="filter-label">Sort by:</span>
            <span class="filter-value">{{ sortBy }}</span>
            <van-icon name="arrow-down" class="filter-arrow" />
        </div>
        <div class="filter-button" @click="handleProviderClick">
            <span class="filter-label">Providers:</span>
            <span class="filter-value">{{ provider }}</span>
            <van-icon name="arrow-down" class="filter-arrow" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

// 定义属性
interface Props {
    sortBy?: string
    provider?: string
}

// 定义事件
interface Emits {
    (e: 'sort-click'): void
    (e: 'provider-click'): void
    (e: 'update:sortBy', value: string): void
    (e: 'update:provider', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
    sortBy: 'Popular',
    provider: 'All',
})

const emit = defineEmits<Emits>()

const sortBy = ref(props.sortBy)
const provider = ref(props.provider)

const handleSortClick = () => {
    emit('sort-click')
}

const handleProviderClick = () => {
    emit('provider-click')
}

// 监听外部值变化
watch(
    () => props.sortBy,
    (newValue) => {
        sortBy.value = newValue
    }
)

watch(
    () => props.provider,
    (newValue) => {
        provider.value = newValue
    }
)

// 监听内部值变化，同步到外部
watch(sortBy, (newValue) => {
    emit('update:sortBy', newValue)
})

watch(provider, (newValue) => {
    emit('update:provider', newValue)
})
</script>

<style lang="scss" scoped>
$text-primary: #ffffff;
$background-color: rgba(255, 255, 255, 0.1);

.filters {
    display: flex;
    gap: 12px;
    padding: 0 16px 16px;
    flex-shrink: 0;

    .filter-button {
        flex: 1;
        display: flex;
        align-items: center;
        background: $background-color;
        border-radius: 6px;
        padding: 12px 16px;
        height: 80px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;

        &:hover {
            opacity: 0.8;
        }

        .filter-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            margin-right: 4px;
            white-space: nowrap;
        }

        .filter-value {
            font-size: 14px;
            color: $text-primary;
            font-weight: 500;
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .filter-arrow {
            width: 16px;
            height: 16px;
            color: rgba(255, 255, 255, 0.6);
            flex-shrink: 0;
            margin-left: 8px;
        }
    }
}
</style>
