<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 15:59:30
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-29 17:19:06
 * @FilePath     : /src/components/searchGame/index.vue
 * @Description  : 游戏搜索页面内容组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 15:59:30
-->

<template>
    <div class="search-game-container">
        <!-- 1. 导航条 -->
        <SearchHeader @close="handleClose" />

        <!-- 2. 搜索栏 -->
        <SearchBar
            v-model="searchKeyword"
            :selected-category="selectedCategoryName"
            @search="performSearch"
            @category-click="showCategorySheet = true"
        />

        <!-- 3. 分类标签 -->
        <GameCategories :selected-category="selectedCategory" :categories="gameCategories" @category-select="selectCategory" />

        <!-- 4. 排序筛选器 -->
        <GameFilters
            v-model:sort-by="sortBy"
            v-model:provider="provider"
            @sort-click="showSortSheet = true"
            @provider-click="showProviderSheet = true"
        />

        <!-- 5. 游戏列表 -->
        <GameGrid :games="filteredGames" @game-select="selectGame" />

        <!-- 分类选择器 -->
        <van-action-sheet v-model:show="showCategorySheet" :actions="categoryActions" @select="onCategorySelect" />

        <!-- 排序选择器 -->
        <van-action-sheet v-model:show="showSortSheet" :actions="sortActions" @select="onSortSelect" />

        <!-- 供应商选择器 -->
        <van-action-sheet v-model:show="showProviderSheet" :actions="providerActions" @select="onProviderSelect" />
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import SearchHeader from './SearchHeader.vue'
import SearchBar from './SearchBar.vue'
import GameCategories from './GameCategories.vue'
import GameFilters from './GameFilters.vue'
import GameGrid from './GameGrid.vue'

// 定义事件
interface Emits {
    (e: 'close'): void
}

const emit = defineEmits<Emits>()

// 搜索相关状态
const searchKeyword = ref('')

// 分类和筛选状态
const selectedCategory = ref('all')
const sortBy = ref('Popular')
const provider = ref('All')

// 弹窗状态
const showCategorySheet = ref(false)
const showSortSheet = ref(false)
const showProviderSheet = ref(false)

// 游戏分类数据
const gameCategories = ref([
    { id: 'all', name: 'All games', icon: 'wap-home-o' }, // 房屋图标
    { id: 'original', name: 'Original', icon: 'star-o' }, // 星形图标
    { id: 'slots', name: 'Slots', icon: 'diamond-o' }, // 钻石图标
    { id: 'live', name: 'Live', icon: 'play-circle-o' }, // 播放图标
    { id: 'lottery', name: 'Lottery', icon: 'contact' }, // @符号图标
    { id: 'sports', name: 'Sports', icon: 'medal-o' }, // 奖牌图标
    { id: 'poker', name: 'Poker', icon: 'fire-o' }, // 火焰图标
    { id: 'fishing', name: 'Fishing', icon: 'location-o' }, // 位置图标
    { id: 'arcade', name: 'Arcade', icon: 'gem-o' }, // 宝石图标
    { id: 'crash', name: 'Crash', icon: 'warning-o' }, // 警告图标
    { id: 'table', name: 'Table', icon: 'apps-o' }, // 应用图标
    { id: 'virtual', name: 'Virtual', icon: 'tv-o' }, // 电视图标
])

// 模拟游戏数据
const gamesList = ref([
    {
        id: 1,
        name: 'LUCKY GAME',
        provider: 'RECTANGLE',
        image: 'https://web-res-ccc.afunimg8.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/010/3010033.jpg',
        tag: 'HOT',
        category: 'slots',
    },
    {
        id: 2,
        name: 'FORTUNE GEMS',
        provider: 'TADA',
        image: 'https://web-res-aaa.afunimg5.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/008/100802070.jpg',
        tag: 'NEW',
        category: 'slots',
    },
    {
        id: 3,
        name: 'BINGO',
        provider: 'JILI',
        image: 'https://web-res-aaa.afunimg5.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/107/110702332.jpg',
        tag: 'NEW',
        category: 'lottery',
    },
    {
        id: 4,
        name: 'SWAGGY GAME',
        provider: 'RECTANGLE',
        image: 'https://web-res-ccc.afunimg8.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/010/3010001.jpg',
        tag: 'HOT',
        category: 'slots',
    },
    {
        id: 5,
        name: 'MINES2',
        provider: 'AFUN MX',
        image: 'https://web-res-ccc.afunimg8.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/0/5021.jpg',
        tag: 'TOP',
        category: 'original',
    },
    {
        id: 6,
        name: 'FORTUNE',
        provider: 'PG SOFT',
        image: 'https://web-res-ccc.afunimg8.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/004/100402059.jpg',
        tag: 'TOP',
        category: 'slots',
    },
    {
        id: 7,
        name: 'FORTUNE GEMS',
        provider: 'TADA',
        image: 'https://web-res-ccc.afunimg8.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/008/100802007.jpg',
        tag: 'HOT',
        category: 'slots',
    },
    {
        id: 8,
        name: 'FORTUNE PIG',
        provider: 'RECTANGLE',
        image: 'https://web-res-aaa.afunimg5.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/010/3010034.jpg',
        tag: 'HOT',
        category: 'slots',
    },
    {
        id: 9,
        name: 'AZTEC GEMS',
        provider: 'PRAGMATIC PLAY',
        image: 'https://web-res-aaa.afunimg5.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/006/100602010.jpg',
        tag: '',
        category: 'slots',
    },
    {
        id: 10,
        name: 'AVIATOR',
        provider: 'SPRIBE',
        image: 'https://web-res-ccc.afunimg8.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/008/100802155.jpg',
        tag: 'TOP',
        category: 'original',
    },
    {
        id: 11,
        name: 'PROSPERITY RABBIT',
        provider: 'PG SOFT',
        image: 'https://web-res-ccc.afunimg8.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/0/1027.jpg',
        tag: '',
        category: 'slots',
    },
    {
        id: 12,
        name: 'GOLDEN DRAGON',
        provider: 'RECTANGLE',
        image: 'https://web-res-ccc.afunimg8.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/010/3010039.jpg',
        tag: 'HOT',
        category: 'slots',
    },
    {
        id: 13,
        name: 'MEGA FORTUNE',
        provider: 'TADA',
        image: 'https://web-res-ccc.afunimg8.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/010/3010033.jpg',
        tag: 'NEW',
        category: 'slots',
    },
    {
        id: 14,
        name: 'LIVE BLACKJACK',
        provider: 'EVOLUTION',
        image: 'https://web-res-aaa.afunimg5.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/008/100802070.jpg',
        tag: 'TOP',
        category: 'live',
    },
    {
        id: 15,
        name: 'ROULETTE ROYAL',
        provider: 'EVOLUTION',
        image: 'https://web-res-aaa.afunimg5.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/107/110702332.jpg',
        tag: 'HOT',
        category: 'live',
    },
    {
        id: 16,
        name: 'CRASH GAME',
        provider: 'AFUN MX',
        image: 'https://web-res-ccc.afunimg8.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/010/3010001.jpg',
        tag: 'NEW',
        category: 'original',
    },
    {
        id: 17,
        name: 'DICE MASTER',
        provider: 'AFUN MX',
        image: 'https://web-res-ccc.afunimg8.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/0/5021.jpg',
        tag: 'TOP',
        category: 'original',
    },
    {
        id: 18,
        name: 'SUPER LOTTERY',
        provider: 'JILI',
        image: 'https://web-res-ccc.afunimg8.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/004/100402059.jpg',
        tag: 'HOT',
        category: 'lottery',
    },
    {
        id: 19,
        name: 'POWER BALL',
        provider: 'JILI',
        image: 'https://web-res-ccc.afunimg8.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/008/100802007.jpg',
        tag: 'NEW',
        category: 'lottery',
    },
    {
        id: 20,
        name: 'MAGIC SLOTS',
        provider: 'PG SOFT',
        image: 'https://web-res-aaa.afunimg5.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/010/3010034.jpg',
        tag: 'TOP',
        category: 'slots',
    },
    {
        id: 21,
        name: 'DRAGON TIGER',
        provider: 'EVOLUTION',
        image: 'https://web-res-aaa.afunimg5.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/006/100602010.jpg',
        tag: 'HOT',
        category: 'live',
    },
    {
        id: 22,
        name: 'BACCARAT VIP',
        provider: 'EVOLUTION',
        image: 'https://web-res-ccc.afunimg8.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/008/100802155.jpg',
        tag: 'NEW',
        category: 'live',
    },
    {
        id: 23,
        name: 'WHEEL OF FORTUNE',
        provider: 'AFUN MX',
        image: 'https://web-res-ccc.afunimg8.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/0/1027.jpg',
        tag: 'TOP',
        category: 'original',
    },
    {
        id: 24,
        name: 'MEGA JACKPOT',
        provider: 'RECTANGLE',
        image: 'https://web-res-ccc.afunimg8.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/010/3010039.jpg',
        tag: 'HOT',
        category: 'slots',
    },
    {
        id: 25,
        name: 'LUCKY NUMBERS',
        provider: 'JILI',
        image: 'https://web-res-ccc.afunimg8.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/010/3010033.jpg',
        tag: 'NEW',
        category: 'lottery',
    },
    {
        id: 26,
        name: 'TREASURE HUNT',
        provider: 'TADA',
        image: 'https://web-res-aaa.afunimg5.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/008/100802070.jpg',
        tag: 'TOP',
        category: 'slots',
    },
    {
        id: 27,
        name: 'POKER MASTER',
        provider: 'EVOLUTION',
        image: 'https://web-res-aaa.afunimg5.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/107/110702332.jpg',
        tag: 'HOT',
        category: 'live',
    },
    {
        id: 28,
        name: 'COIN FLIP',
        provider: 'AFUN MX',
        image: 'https://web-res-ccc.afunimg8.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/010/3010001.jpg',
        tag: 'NEW',
        category: 'original',
    },
    {
        id: 29,
        name: 'FRUIT MACHINE',
        provider: 'PG SOFT',
        image: 'https://web-res-ccc.afunimg8.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/0/5021.jpg',
        tag: 'TOP',
        category: 'slots',
    },
    {
        id: 30,
        name: 'KENO CLASSIC',
        provider: 'JILI',
        image: 'https://web-res-ccc.afunimg8.com/cdn-cgi/image/f=webp,w=110.33,dpr=3,q=80/newres/gameicon_en6020/004/100402059.jpg',
        tag: 'HOT',
        category: 'lottery',
    },
])

// 选择器选项
const categoryActions = ref([
    { name: 'Casino', value: 'casino' },
    { name: 'Sports', value: 'sports' },
    { name: 'Live Casino', value: 'live' },
])

const sortActions = ref([
    { name: 'Popular', value: 'popular' },
    { name: 'Newest', value: 'newest' },
    { name: 'A-Z', value: 'az' },
    { name: 'Z-A', value: 'za' },
])

const providerActions = ref([
    { name: 'All', value: 'all' },
    { name: 'TADA', value: 'tada' },
    { name: 'RECTANGLE', value: 'rectangle' },
    { name: 'PG SOFT', value: 'pgsoft' },
    { name: 'PRAGMATIC PLAY', value: 'pragmatic' },
    { name: 'SPRIBE', value: 'spribe' },
])

// 计算属性：选中分类的名称
const selectedCategoryName = computed(() => {
    const category = gameCategories.value.find((cat) => cat.id === selectedCategory.value)
    return category ? category.name : 'Casino'
})

// 计算属性：过滤后的游戏列表
const filteredGames = computed(() => {
    let filtered = gamesList.value

    // 按分类过滤
    if (selectedCategory.value !== 'all') {
        filtered = filtered.filter((game) => game.category === selectedCategory.value)
    }

    // 按搜索关键词过滤
    if (searchKeyword.value) {
        filtered = filtered.filter(
            (game) =>
                game.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
                game.provider.toLowerCase().includes(searchKeyword.value.toLowerCase())
        )
    }

    // 按供应商过滤
    if (provider.value !== 'All') {
        filtered = filtered.filter((game) => game.provider.toLowerCase().includes(provider.value.toLowerCase()))
    }

    return filtered
})

// 方法
const handleClose = () => {
    emit('close')
}

const performSearch = () => {
    console.log('执行搜索:', searchKeyword.value)
}

const selectCategory = (categoryId: string) => {
    selectedCategory.value = categoryId
}

const selectGame = (game: any) => {
    console.log('选择游戏:', game)
    // 这里可以添加游戏选择逻辑
}

const onCategorySelect = (action: any) => {
    console.log('选择分类:', action)
    showCategorySheet.value = false
}

const onSortSelect = (action: any) => {
    sortBy.value = action.name
    showSortSheet.value = false
}

const onProviderSelect = (action: any) => {
    provider.value = action.name
    showProviderSheet.value = false
}
</script>

<style lang="scss" scoped>
$bg-primary: #2b2b2b;
$text-primary: #ffffff;

.search-game-container {
    background: $bg-primary;
    height: 100vh;
    display: flex;
    flex-direction: column;
    color: $text-primary;
    overflow: auto;
}
</style>
